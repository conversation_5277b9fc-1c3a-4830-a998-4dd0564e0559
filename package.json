{"name": "magicui-portfolio-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "setup-spotify": "ts-node -P tsconfig.script.json scripts/setup-spotify.ts"}, "dependencies": {"@ionic/react": "^8.2.7", "@lottiefiles/dotlottie-react": "^0.13.3", "@next/third-parties": "^15.3.3", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-slot": "^1.2.0", "@react-three/drei": "^9.68.2", "@react-three/fiber": "^8.13.0", "@supabase/supabase-js": "^2.49.4", "@types/three": "0.152.1", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "aws-sdk": "^2.1678.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.18.2", "gtag": "^1.0.1", "leva": "^0.9.35", "lottie-react": "^2.4.1", "lucide-react": "^0.428.0", "next": "^15.2.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^9.0.1", "swr": "^2.3.3", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "three": "^0.146.0"}, "devDependencies": {"@types/cookie": "^0.6.0", "@types/express": "^4.17.21", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "axios": "^1.6.2", "dotenv": "^16.3.1", "eslint": "^8", "eslint-config-next": "14.2.5", "express": "^4.18.2", "open": "^9.1.0", "postcss": "^8", "tailwindcss": "^3.4.9", "ts-node": "^10.9.1", "typescript": "^5"}}