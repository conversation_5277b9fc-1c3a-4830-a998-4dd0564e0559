{"compilerOptions": {"target": "es2018", "module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "outDir": "dist", "typeRoots": ["./node_modules/@types"], "lib": ["ES2021"]}, "include": ["scripts/**/*.ts"], "exclude": ["node_modules"]}